import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试净费率计算逻辑
 */
public class TestNetRateCalculation {
    
    public static void main(String[] args) {
        // 模拟WithdrawRecordAndReteEntityDTO
        TestRateDTO rateDTO = new TestRateDTO();
        rateDTO.setDyRate(new BigDecimal(10));   // 抖音
        rateDTO.setWxRate(new BigDecimal(10));   // 微信
        rateDTO.setKsRate(new BigDecimal(10));   // 快手
        rateDTO.setSphRate(new BigDecimal(11));  // 视频号
        rateDTO.setZsdpRate(new BigDecimal(10)); // 知识店铺
        rateDTO.setPcRate(new BigDecimal(10));   // PC端
        rateDTO.setH5Rate(new BigDecimal(10));   // H5端
        rateDTO.setXhsRate(new BigDecimal(11));  // 小红书
        rateDTO.setDdRate(new BigDecimal(11));   // 抖店
        
        // 测试原费率映射
        Map<Integer, BigDecimal> originalRateMap = generateRateMap(rateDTO);
        System.out.println("原费率映射:");
        printRateMap(originalRateMap);
        
        // 测试净费率映射
        Map<Integer, BigDecimal> netRateMap = generateNetRateMap(rateDTO);
        System.out.println("\n净费率映射:");
        printRateMap(netRateMap);
        
        // 验证扣减逻辑
        System.out.println("\n费率扣减验证:");
        System.out.println("抖音小程序(platform=0): 原值" + originalRateMap.get(0) + " -> 净值" + netRateMap.get(0) + " (应保持不变)");
        System.out.println("微信小程序(platform=1): 原值" + originalRateMap.get(1) + " -> 净值" + netRateMap.get(1) + " (应减1)");
        System.out.println("快手小程序(platform=2): 原值" + originalRateMap.get(2) + " -> 净值" + netRateMap.get(2) + " (应减2)");
        System.out.println("视频号(platform=3): 原值" + originalRateMap.get(3) + " -> 净值" + netRateMap.get(3) + " (应减3)");
        System.out.println("知识店铺(platform=8): 原值" + originalRateMap.get(8) + " -> 净值" + netRateMap.get(8) + " (应减1)");
        System.out.println("PC端(platform=9): 原值" + originalRateMap.get(9) + " -> 净值" + netRateMap.get(9) + " (应减1)");
        System.out.println("H5端(platform=5): 原值" + originalRateMap.get(5) + " -> 净值" + netRateMap.get(5) + " (应减1)");
        System.out.println("小红书(platform=7): 原值" + originalRateMap.get(7) + " -> 净值" + netRateMap.get(7) + " (应减3)");
        System.out.println("抖店(platform=11): 原值" + originalRateMap.get(11) + " -> 净值" + netRateMap.get(11) + " (应减2)");
    }
    
    private static void printRateMap(Map<Integer, BigDecimal> rateMap) {
        for (Map.Entry<Integer, BigDecimal> entry : rateMap.entrySet()) {
            String platformName = getPlatformName(entry.getKey());
            System.out.println("Platform " + entry.getKey() + " (" + platformName + "): " + entry.getValue());
        }
    }
    
    private static String getPlatformName(Integer platform) {
        switch (platform) {
            case 0: return "抖音小程序";
            case 1: return "微信小程序";
            case 2: return "快手小程序";
            case 3: return "视频号";
            case 5: return "H5端";
            case 7: return "小红书";
            case 8: return "知识店铺";
            case 9: return "PC端";
            case 11: return "抖店";
            default: return "未知平台";
        }
    }
    
    // 复制原有的generateRateMap方法
    private static Map<Integer, BigDecimal> generateRateMap(TestRateDTO rateDTO) {
        Map<Integer, BigDecimal> rateMap = new HashMap<>();
        rateMap.put(0, rateDTO.getDyRate() == null ? new BigDecimal(10) : rateDTO.getDyRate());
        rateMap.put(1, rateDTO.getWxRate() == null ? new BigDecimal(10) : rateDTO.getWxRate());
        rateMap.put(2, rateDTO.getKsRate() == null ? new BigDecimal(10) : rateDTO.getKsRate());
        rateMap.put(3, rateDTO.getSphRate() == null ? new BigDecimal(11) : rateDTO.getSphRate());
        rateMap.put(8, rateDTO.getZsdpRate() == null ? new BigDecimal(10) : rateDTO.getZsdpRate());
        rateMap.put(9, rateDTO.getPcRate() == null ? new BigDecimal(10) : rateDTO.getPcRate());
        rateMap.put(5, rateDTO.getH5Rate() == null ? new BigDecimal(10) : rateDTO.getH5Rate());
        rateMap.put(7, rateDTO.getXhsRate() == null ? new BigDecimal(11) : rateDTO.getXhsRate());
        rateMap.put(11, rateDTO.getDdRate() == null ? new BigDecimal(11) : rateDTO.getDdRate());
        return rateMap;
    }
    
    // 复制新实现的generateNetRateMap方法
    private static Map<Integer, BigDecimal> generateNetRateMap(TestRateDTO rateDTO) {
        Map<Integer, BigDecimal> netRateMap = new HashMap<>();
        // 抖音小程序: 原值保持不变
        netRateMap.put(0, rateDTO.getDyRate() == null ? new BigDecimal(10) : rateDTO.getDyRate());
        // 微信小程序: 原值减1
        BigDecimal wxRate = rateDTO.getWxRate() == null ? new BigDecimal(10) : rateDTO.getWxRate();
        netRateMap.put(1, wxRate.subtract(BigDecimal.ONE));
        // 快手小程序: 原值减2
        BigDecimal ksRate = rateDTO.getKsRate() == null ? new BigDecimal(10) : rateDTO.getKsRate();
        netRateMap.put(2, ksRate.subtract(new BigDecimal(2)));
        // 视频号: 原值减3
        BigDecimal sphRate = rateDTO.getSphRate() == null ? new BigDecimal(11) : rateDTO.getSphRate();
        netRateMap.put(3, sphRate.subtract(new BigDecimal(3)));
        // 知识店铺: 原值减1
        BigDecimal zsdpRate = rateDTO.getZsdpRate() == null ? new BigDecimal(10) : rateDTO.getZsdpRate();
        netRateMap.put(8, zsdpRate.subtract(BigDecimal.ONE));
        // pc端: 原值减1
        BigDecimal pcRate = rateDTO.getPcRate() == null ? new BigDecimal(10) : rateDTO.getPcRate();
        netRateMap.put(9, pcRate.subtract(BigDecimal.ONE));
        // h5端: 原值减1
        BigDecimal h5Rate = rateDTO.getH5Rate() == null ? new BigDecimal(10) : rateDTO.getH5Rate();
        netRateMap.put(5, h5Rate.subtract(BigDecimal.ONE));
        // 小红书: 原值减3
        BigDecimal xhsRate = rateDTO.getXhsRate() == null ? new BigDecimal(11) : rateDTO.getXhsRate();
        netRateMap.put(7, xhsRate.subtract(new BigDecimal(3)));
        // 抖店: 原值减2
        BigDecimal ddRate = rateDTO.getDdRate() == null ? new BigDecimal(11) : rateDTO.getDdRate();
        netRateMap.put(11, ddRate.subtract(new BigDecimal(2)));
        return netRateMap;
    }
    
    // 简单的测试DTO类
    static class TestRateDTO {
        private BigDecimal dyRate;
        private BigDecimal wxRate;
        private BigDecimal ksRate;
        private BigDecimal sphRate;
        private BigDecimal zsdpRate;
        private BigDecimal pcRate;
        private BigDecimal h5Rate;
        private BigDecimal xhsRate;
        private BigDecimal ddRate;
        
        // Getters and Setters
        public BigDecimal getDyRate() { return dyRate; }
        public void setDyRate(BigDecimal dyRate) { this.dyRate = dyRate; }
        public BigDecimal getWxRate() { return wxRate; }
        public void setWxRate(BigDecimal wxRate) { this.wxRate = wxRate; }
        public BigDecimal getKsRate() { return ksRate; }
        public void setKsRate(BigDecimal ksRate) { this.ksRate = ksRate; }
        public BigDecimal getSphRate() { return sphRate; }
        public void setSphRate(BigDecimal sphRate) { this.sphRate = sphRate; }
        public BigDecimal getZsdpRate() { return zsdpRate; }
        public void setZsdpRate(BigDecimal zsdpRate) { this.zsdpRate = zsdpRate; }
        public BigDecimal getPcRate() { return pcRate; }
        public void setPcRate(BigDecimal pcRate) { this.pcRate = pcRate; }
        public BigDecimal getH5Rate() { return h5Rate; }
        public void setH5Rate(BigDecimal h5Rate) { this.h5Rate = h5Rate; }
        public BigDecimal getXhsRate() { return xhsRate; }
        public void setXhsRate(BigDecimal xhsRate) { this.xhsRate = xhsRate; }
        public BigDecimal getDdRate() { return ddRate; }
        public void setDdRate(BigDecimal ddRate) { this.ddRate = ddRate; }
    }
}
